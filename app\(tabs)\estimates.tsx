// app/(tabs)/estimates.tsx

import React, { useEffect, useState } from 'react';
import {
    Alert,
    FlatList,
    RefreshControl,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ESTIMATE_STATUSES } from '@/constants/categories';
import { FontSizes, FontWeights, Spacing } from '@/constants/Colors';
import { useAuth } from '@/hooks/useAuth';
import { useTheme } from '@/hooks/useTheme';
import { Estimate } from '@/types/models';
import { estimateApi } from '@/utils/api';
import { formatCurrency } from '@/utils/calculations';


export default function EstimatesScreen() {
  const { colors } = useTheme();
  const { hasPermission } = useAuth();
  const [estimates, setEstimates] = useState<Estimate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [selectedEstimate, setSelectedEstimate] = useState<Estimate | undefined>();

  useEffect(() => {
    loadEstimates();
  }, []);

  const loadEstimates = async () => {
    try {
      const response = await estimateApi.getAll();
      if (response.success && response.data) {
        setEstimates(response.data);
      } else {
        Alert.alert('Error', response.error || 'Failed to load estimates');
      }
    } catch (error) {
      console.error('Estimates error:', error);
      Alert.alert('Error', 'Failed to load estimates');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadEstimates();
    setRefreshing(false);
  };

  const handleAddEstimate = () => {
    setSelectedEstimate(undefined);
    setShowModal(true);
  };

  const handleEditEstimate = (estimate: Estimate) => {
    setSelectedEstimate(estimate);
    setShowModal(true);
  };

  const handleDeleteEstimate = (estimate: Estimate) => {
    Alert.alert(
      'Delete Estimate',
      `Are you sure you want to delete the estimate for ${estimate.projectName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const result = await estimateApi.delete(estimate.id);
            if (result.success) {
              setEstimates(prev => prev.filter(e => e.id !== estimate.id));
              Alert.alert('Success', 'Estimate deleted successfully');
            } else {
              Alert.alert('Error', result.error || 'Failed to delete estimate');
            }
          },
        },
      ]
    );
  };

  const handleWizardSuccess = (estimate: Estimate) => {
    setShowModal(false);
    setSelectedEstimate(undefined);
    loadEstimates(); // Refresh the list
  };

  const handleWizardCancel = () => {
    setShowModal(false);
    setSelectedEstimate(undefined);
  };

  const getStatusConfig = (status: string) => {
    return ESTIMATE_STATUSES.find(s => s.value === status) || ESTIMATE_STATUSES[0];
  };

  const renderEstimateItem = ({ item }: { item: Estimate }) => {
    const statusConfig = getStatusConfig(item.status);

    return (
      <View style={styles.estimateCard}>
        <View style={styles.estimateHeader}>
          <Text style={styles.projectName}>{item.projectName}</Text>
          <View style={[styles.statusBadge, { backgroundColor: statusConfig.color }]}>
            <Text style={styles.statusText}>{statusConfig.label}</Text>
          </View>
        </View>

        <Text style={styles.projectDescription} numberOfLines={2}>
          {item.projectDescription}
        </Text>

        <View style={styles.estimateDetails}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Start Date:</Text>
            <Text style={styles.detailValue}>
              {new Date(item.startDate).toLocaleDateString()}
            </Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>End Date:</Text>
            <Text style={styles.detailValue}>
              {new Date(item.endDate).toLocaleDateString()}
            </Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Items:</Text>
            <Text style={styles.detailValue}>{item.items.length}</Text>
          </View>
        </View>

        <View style={styles.estimateFooter}>
          <View style={styles.estimateInfo}>
            <Text style={styles.totalLabel}>Total Amount</Text>
            <Text style={styles.totalAmount}>
              {formatCurrency(item.grandTotal)}
            </Text>
            <Text style={styles.estimateDate}>
              Created {new Date(item.createdAt).toLocaleDateString()}
            </Text>
          </View>
          {hasPermission('manage_estimates') && (
            <View style={styles.estimateActions}>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => handleEditEstimate(item)}
              >
                <Text style={styles.editButtonText}>Edit</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={() => handleDeleteEstimate(item)}
              >
                <Text style={styles.deleteButtonText}>Delete</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      padding: Spacing.lg,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
    },
    headerText: {
      flex: 1,
      marginRight: Spacing.md,
    },
    title: {
      fontSize: FontSizes.xxl,
      fontWeight: FontWeights.bold,
      color: colors.text,
      marginBottom: Spacing.xs,
    },
    subtitle: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
    },
    content: {
      flex: 1,
      padding: Spacing.lg,
    },
    estimateCard: {
      backgroundColor: colors.card,
      padding: Spacing.lg,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: Spacing.md,
    },
    estimateHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: Spacing.sm,
    },
    projectName: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: colors.text,
      flex: 1,
    },
    statusBadge: {
      paddingHorizontal: Spacing.sm,
      paddingVertical: Spacing.xs,
      borderRadius: 12,
    },
    statusText: {
      fontSize: FontSizes.xs,
      fontWeight: FontWeights.medium,
      color: '#FFFFFF',
    },
    projectDescription: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
      marginBottom: Spacing.md,
    },
    estimateDetails: {
      marginBottom: Spacing.md,
    },
    detailRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: Spacing.xs,
    },
    detailLabel: {
      fontSize: FontSizes.sm,
      color: colors.textSecondary,
    },
    detailValue: {
      fontSize: FontSizes.sm,
      fontWeight: FontWeights.medium,
      color: colors.text,
    },
    estimateFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-end',
      marginTop: Spacing.sm,
    },
    estimateInfo: {
      flex: 1,
    },
    estimateActions: {
      flexDirection: 'row',
      gap: Spacing.sm,
    },
    editButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: Spacing.md,
      paddingVertical: Spacing.sm,
      borderRadius: 6,
    },
    editButtonText: {
      fontSize: FontSizes.sm,
      fontWeight: FontWeights.medium,
      color: '#FFFFFF',
    },
    deleteButton: {
      backgroundColor: colors.error,
      paddingHorizontal: Spacing.md,
      paddingVertical: Spacing.sm,
      borderRadius: 6,
    },
    deleteButtonText: {
      fontSize: FontSizes.sm,
      fontWeight: FontWeights.medium,
      color: '#FFFFFF',
    },
    totalLabel: {
      fontSize: FontSizes.sm,
      color: colors.textSecondary,
      marginBottom: Spacing.xs,
    },
    totalAmount: {
      fontSize: FontSizes.xl,
      fontWeight: FontWeights.bold,
      color: colors.primary,
    },
    estimateDate: {
      fontSize: FontSizes.xs,
      color: colors.textLight,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: Spacing.xl,
    },
    emptyText: {
      fontSize: FontSizes.lg,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: FontSizes.md,
      color: colors.textSecondary,
    },
  });

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading estimates...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.headerText}>
            <Text style={styles.title}>Estimates</Text>
            <Text style={styles.subtitle}>
              Track and manage your project estimates
            </Text>
          </View>
          {hasPermission('manage_estimates') && (
            <Button
              title="New Estimate"
              onPress={handleAddEstimate}
              size="small"
            />
          )}
        </View>
      </View>

      <View style={styles.content}>
        <FlatList
          data={estimates}
          renderItem={renderEstimateItem}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>
                No estimates found.{'\n'}
                {hasPermission('manage_estimates')
                  ? 'Create your first estimate to get started!'
                  : 'Contact your administrator to create estimates.'}
              </Text>
            </View>
          }
          showsVerticalScrollIndicator={false}
        />
      </View>

      <Modal
        visible={showModal}
        onClose={handleWizardCancel}
        title={selectedEstimate ? 'Edit Estimate' : 'Create New Estimate'}
        size="fullscreen"
      >
        <EstimateWizard
          estimate={selectedEstimate}
          onSuccess={handleWizardSuccess}
          onCancel={handleWizardCancel}
        />
      </Modal>
    </SafeAreaView>
  );
}
